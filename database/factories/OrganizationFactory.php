<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Enums\CompanyType;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Organization>
 */
class OrganizationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company(),
            'description' => fake()->sentence(),
            'is_active' => true,
            'is_suspended' => false,
            'default_flow_id' => null,
            'email' => fake()->unique()->safeEmail(),
            'cpf_cnpj' => fake()->numerify('##############'),
            'company_type' => fake()->randomElement(CompanyType::cases()),
            'phone' => fake()->phoneNumber(),
            'mobile_phone' => fake()->phoneNumber(),
            'address' => fake()->streetAddress(),
            'address_number' => fake()->buildingNumber(),
            'complement' => fake()->optional()->secondaryAddress(),
            'province' => fake()->citySuffix(),
            'postal_code' => fake()->postcode(),
        ];
    }

    /**
     * Indicate that the organization is suspended.
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_suspended' => true,
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the organization is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Organization with basic contact info for ASAAS
     */
    public function withContactInfo(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => fake()->company(),
            'description' => fake()->sentence(),
        ]);
    }
}
