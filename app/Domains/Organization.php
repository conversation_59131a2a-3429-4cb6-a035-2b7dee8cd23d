<?php

namespace App\Domains;

use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Domains\AsaasOrganizationCustomer;
use App\Enums\CompanyType;
use App\Helpers\Traits\PeopleTyped;
use App\Services\ASAAS\Exceptions\AsaasException;
use Carbon\Carbon;

class Organization
{

    use PeopleTyped;

    public const DEFAULT_COMPANY_TYPE = 'MEI';

    public ?int $id;
    public string $name;
    public ?string $description;
    public ?bool $is_active;
    public ?bool $is_suspended;
    public ?int $default_flow_id;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    // Contact Information
    public ?string $email;
    public ?string $cpf_cnpj;
    public ?CompanyType $company_type;
    public ?string $phone;
    public ?string $mobile_phone;
    public ?string $address;
    public ?string $address_number;
    public ?string $complement;
    public ?string $province;
    public ?string $postal_code;

    // Independent Courtesy System
    public ?bool $is_courtesy;
    public ?Carbon $courtesy_expires_at;
    public ?string $courtesy_reason;

    // ASAAS Relationships
    public ?AsaasOrganization $asaas;
    public ?AsaasOrganizationCustomer $asaasCustomer;

    public function __construct(
        ?int $id,
        string $name,
        ?string $description,
        ?bool $is_active,
        ?bool $is_suspended,
        ?int $default_flow_id = null,
        ?string $email = null,
        ?string $cpf_cnpj = null,
        ?CompanyType $company_type = null,
        ?string $phone = null,
        ?string $mobile_phone = null,
        ?string $address = null,
        ?string $address_number = null,
        ?string $complement = null,
        ?string $province = null,
        ?string $postal_code = null,
        ?bool $is_courtesy = null,
        ?Carbon $courtesy_expires_at = null,
        ?string $courtesy_reason = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?AsaasOrganization $asaas = null,
        ?AsaasOrganizationCustomer $asaasCustomer = null,
    ){
        $this->id = $id;
        $this->name = $name;
        $this->description = $description;
        $this->is_active = $is_active;
        $this->is_suspended = $is_suspended;
        $this->default_flow_id = $default_flow_id;
        $this->email = $email;
        $this->cpf_cnpj = $cpf_cnpj;
        $this->company_type = $company_type;
        $this->phone = $phone;
        $this->mobile_phone = $mobile_phone;
        $this->address = $address;
        $this->address_number = $address_number;
        $this->complement = $complement;
        $this->province = $province;
        $this->postal_code = $postal_code;
        $this->is_courtesy = $is_courtesy;
        $this->courtesy_expires_at = $courtesy_expires_at;
        $this->courtesy_reason = $courtesy_reason;
        $this->asaas = $asaas;
        $this->asaasCustomer = $asaasCustomer;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;

        $this->document = $this->cpf_cnpj;
        $this->setDocumentOnlyNumbers();
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_suspended" => $this->is_suspended,
            "email" => $this->email,
            "cpf_cnpj" => $this->cpf_cnpj,
            "company_type" => $this->company_type?->value,
            "phone" => $this->phone,
            "mobile_phone" => $this->mobile_phone,
            "address" => $this->address,
            "address_number" => $this->address_number,
            "complement" => $this->complement,
            "province" => $this->province,
            "postal_code" => $this->postal_code,
            "is_courtesy" => $this->is_courtesy,
            "courtesy_expires_at" => $this->courtesy_expires_at?->format("Y-m-d"),
            "courtesy_reason" => $this->courtesy_reason,
            "created_at" => $this->created_at?->format("Y-m-d H:i:s"),
            "updated_at" => $this->updated_at?->format("Y-m-d H:i:s")
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_suspended" => $this->is_suspended,
            "email" => $this->email,
            "cpf_cnpj" => $this->cpf_cnpj,
            "company_type" => $this->company_type,
            "phone" => $this->phone,
            "mobile_phone" => $this->mobile_phone,
            "address" => $this->address,
            "address_number" => $this->address_number,
            "complement" => $this->complement,
            "province" => $this->province,
            "postal_code" => $this->postal_code,
            "is_courtesy" => $this->is_courtesy,
            "courtesy_expires_at" => $this->courtesy_expires_at,
            "courtesy_reason" => $this->courtesy_reason,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_suspended" => $this->is_suspended,
            "email" => $this->email,
            "cpf_cnpj" => $this->cpf_cnpj,
            "company_type" => $this->company_type,
            "phone" => $this->phone,
            "mobile_phone" => $this->mobile_phone,
            "address" => $this->address,
            "address_number" => $this->address_number,
            "complement" => $this->complement,
            "province" => $this->province,
            "postal_code" => $this->postal_code,
        ];
    }

    // ========== ASAAS INTEGRATION METHODS ==========

    /**
     * Check if has ASAAS integration
     */
    public function hasAsaasIntegration(): bool
    {
        return $this->asaas !== null && $this->asaas->hasAsaasIntegration();
    }

    /**
     * Check if has ASAAS customer integration
     */
    public function hasAsaasCustomerIntegration(): bool
    {
        return $this->asaasCustomer !== null && $this->asaasCustomer->hasAsaasIntegration();
    }

    /**
     * Get ASAAS subscription summary
     */
    public function getAsaasSubscriptionSummary(): ?array
    {
        return $this->asaas?->getSubscriptionSummary();
    }


    /**
     * Check if has active subscription
     */
    public function hasActiveSubscription(): bool
    {
        return $this->asaas?->hasActiveSubscription() ?? false;
    }

    /**
     * Check if organization is in local courtesy period (independent of ASAAS)
     */
    public function isInLocalCourtesy(): bool
    {
        if (!$this->is_courtesy) {
            return false;
        }

        // If no expiration date, courtesy is permanent
        if (!$this->courtesy_expires_at) {
            return true;
        }

        // Check if courtesy hasn't expired
        return $this->courtesy_expires_at->isFuture();
    }

    /**
     * Check if organization can access a system (local courtesy OR ASAAS)
     */
    public function canAccessSystem(): bool
    {
        // Check local courtesy first
        if ($this->isInLocalCourtesy()) {
            return true;
        }

        // Check ASAAS access
        return $this->asaas?->hasAsaasIntegration() ?? false;
    }

    /**
     * Get comprehensive access summary
     */
    public function getAccessSummary(): array
    {
        $summary = [
            'can_access' => $this->canAccessSystem(),
            'access_type' => 'none',
            'local_courtesy' => [
                'is_courtesy' => $this->is_courtesy ?? false,
                'is_active' => $this->isInLocalCourtesy(),
                'expires_at' => $this->courtesy_expires_at?->toDateString(),
                'reason' => $this->courtesy_reason
            ],
            'asaas_access' => null
        ];

        // Determine access type
        if ($this->isInLocalCourtesy()) {
            $summary['access_type'] = 'local_courtesy';
        } elseif ($this->asaas?->isInCourtesy()) {
            $summary['access_type'] = 'asaas_courtesy';
        } elseif ($this->asaas?->hasActiveSubscription()) {
            $summary['access_type'] = 'subscription';
        }

        // Add ASAAS access info if available
        if ($this->asaas) {
            $summary['asaas_access'] = [
                'has_integration' => true,
                'can_access' => $this->asaas->canAccessSystem(),
                'is_courtesy' => $this->asaas->isInCourtesy(),
                'has_subscription' => $this->asaas->hasActiveSubscription(),
                'subscription_status' => $this->asaas->subscription_status?->value,
                'courtesy_expires_at' => $this->asaas->courtesy_expires_at?->toDateString()
            ];
        }

        return $summary;
    }

    /**
     * Clean postal code removing special characters
     *
     * @return string
     */
    protected function cleanPostalCode(): string
    {
        return preg_replace('/[^0-9]/', '', $this->postal_code);
    }

    public function toAsaasPayload(): array
    {
        return [
            'name' => $this->name,
            'email' => $this->email,
            'cpfCnpj' => $this->document,
            'companyType' => $this->company_type?->value ?? self::DEFAULT_COMPANY_TYPE,
            'phone' => $this->phone,
            'mobilePhone' => $this->mobile_phone,
            'address' => $this->address,
            'addressNumber' => $this->address_number,
            'complement' => $this->complement,
            'province' => $this->province,
            'postalCode' => $this->cleanPostalCode(),
            'externalReference' => $this->id,
            'incomeValue' => 0,
        ];
    }

    /**
     * Validate organization data for ASAAS subaccount creation
     *
     * @throws AsaasException validateAsaasPayload
     */
    public function validateAsaasPayload(): void
    {
        $errors = [];

        if (empty($this->name)) {
            $errors[] = 'Organization name is required';
        }
        if (empty($this->document)) {
            $errors[] = 'Organization email is required';
        }
        if (!$this->isValidDocument()) {
            $errors[] = 'Organization document (CPF/CNPJ) is invalid';
        }

        // Additional fields can be added to AsaasOrganization later
        if (!empty($errors)) {
            throw new AsaasException('Validation failed: ' . implode(', ', $errors));
        }
    }
}
