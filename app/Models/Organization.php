<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Services\ASAAS\Models\AsaasOrganizationCustomer;
use App\Domains\Organization as OrganizationDomain;
use App\Enums\CompanyType;

class Organization extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'organizations';

    protected $fillable = [
        'name',
        'description',
        'is_active',
        'is_suspended',
        'default_flow_id',
        'email',
        'cpf_cnpj',
        'company_type',
        'phone',
        'mobile_phone',
        'address',
        'address_number',
        'complement',
        'province',
        'postal_code',
    ];

    public function users(){
        return $this->hasMany(User::class);
    }
    public function stock_entries(){
        return $this->hasMany(StockEntry::class);
    }
    public function stock_exits(){
        return $this->hasMany(StockExit::class);
    }
    public function stocks(){
        return $this->hasMany(Stock::class);
    }
    public function groups(){
        return $this->hasMany(Group::class);
    }

    public function defaultFlow()
    {
        return $this->belongsTo(Flow::class, 'default_flow_id');
    }

    /**
     * Get the ASAAS integration for this organization
     */
    public function asaas(): HasOne
    {
        return $this->hasOne(AsaasOrganization::class);
    }

    /**
     * Get the ASAAS customer integration for this organization
     */
    public function asaasCustomer(): HasOne
    {
        return $this->hasOne(AsaasOrganizationCustomer::class);
    }

    /**
     * Cast attributes to appropriate types
     */
    protected $casts = [
        'is_active' => 'boolean',
        'is_suspended' => 'boolean',
        'company_type' => CompanyType::class,
    ];

    /**
     * Check if organization has ASAAS integration
     */
    public function hasAsaasIntegration(): bool
    {
        return $this->asaas !== null;
    }

    /**
     * Check if organization has ASAAS customer integration
     */
    public function hasAsaasCustomerIntegration(): bool
    {
        return $this->asaasCustomer !== null;
    }

    /**
     * Get the subscription for this organization
     */
    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class);
    }

    /**
     * Check if organization has active subscription
     */
    public function hasActiveSubscription(): bool
    {
        return $this->subscription !== null && $this->subscription->canAccessSystem();
    }

    /**
     * Get subscription status
     */
    public function getSubscriptionStatus(): string
    {
        // Check if there's an ASAAS subscription first
        if ($this->subscription && $this->subscription->asaasSubscription) {
            return $this->subscription->asaasSubscription->status ?? $this->subscription->status;
        }

        // Fall back to regular subscription status
        return $this->subscription?->status ?? 'NONE';
    }

    /**
     * Check if organization can access the system
     */
    public function canAccessSystem(): bool
    {
        return $this->is_active &&
               !$this->is_suspended &&
               $this->hasAsaasIntegration() &&
               $this->asaas->canAccessSystem();
    }

    /**
     * Get ASAAS account ID (convenience method)
     */
    public function getAsaasAccountId(): ?string
    {
        return $this->asaas?->asaas_account_id;
    }

    /**
     * Get ASAAS API key (convenience method)
     */
    public function getAsaasApiKey(): ?string
    {
        return $this->asaas?->asaas_api_key;
    }

    /**
     * Get ASAAS subscription status (convenience method)
     */
    public function getAsaasSubscriptionStatus(): ?string
    {
        return $this->asaas?->subscription_status?->value;
    }



    /**
     * Convert model to domain
     */
    public function toDomain(): OrganizationDomain
    {
        return new OrganizationDomain(
            id: $this->id,
            name: $this->name,
            description: $this->description,
            is_active: $this->is_active,
            is_suspended: $this->is_suspended,
            default_flow_id: $this->default_flow_id,
            created_at: $this->created_at,
            updated_at: $this->updated_at,
        );
    }
}
