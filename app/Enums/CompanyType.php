<?php

namespace App\Enums;

enum CompanyType: string
{
    // Physical People
    case PHYSICAL_PERSON = 'PHYSICAL_PERSON';
    case INDIVIDUAL_ENTREPRENEUR = 'INDIVIDUAL_ENTREPRENEUR';
    
    // Juridical People - Individual
    case MEI = 'MEI';
    case EIRELI = 'EIRELI';
    
    // Juridical People - Partnerships
    case LTDA = 'LTDA';
    case SA = 'S.A.';
    case SC = 'S/C';
    case SC_LTDA = 'S/C LTDA';
    case SC_MEI = 'S/C MEI';
    case SC_EIRELI = 'S/C EIRELI';
    case SC_LTDA_MEI = 'S/C LTDA MEI';
    case SC_LTDA_EIRELI = 'S/C LTDA EIRELI';
    
    // Additional Juridical People Types
    case SCP = 'SCP'; // Sociedade Civil de Profissionais
    case COOPERATIVA = 'COOPERATIVA';
    case ASSOCIACAO = 'ASSOCIACAO';
    case FUNDACAO = 'FUNDACAO';
    case ONG = 'ONG';
    case OSCIP = 'OSCIP';
    case EMPRESA_PUBLICA = 'EMPRESA_PUBLICA';
    case SOCIEDADE_ECONOMIA_MISTA = 'SOCIEDADE_ECONOMIA_MISTA';

    /**
     * Get all physical people types
     */
    public static function getPhysicalPeopleTypes(): array
    {
        return [
            self::PHYSICAL_PERSON,
            self::INDIVIDUAL_ENTREPRENEUR,
        ];
    }

    /**
     * Get all juridical people types
     */
    public static function getJuridicalPeopleTypes(): array
    {
        return [
            self::MEI,
            self::EIRELI,
            self::LTDA,
            self::SA,
            self::SC,
            self::SC_LTDA,
            self::SC_MEI,
            self::SC_EIRELI,
            self::SC_LTDA_MEI,
            self::SC_LTDA_EIRELI,
            self::SCP,
            self::COOPERATIVA,
            self::ASSOCIACAO,
            self::FUNDACAO,
            self::ONG,
            self::OSCIP,
            self::EMPRESA_PUBLICA,
            self::SOCIEDADE_ECONOMIA_MISTA,
        ];
    }

    /**
     * Check if the company type is physical person
     */
    public function isPhysicalPerson(): bool
    {
        return in_array($this, self::getPhysicalPeopleTypes());
    }

    /**
     * Check if the company type is juridical person
     */
    public function isJuridicalPerson(): bool
    {
        return in_array($this, self::getJuridicalPeopleTypes());
    }

    /**
     * Get human readable label
     */
    public function getLabel(): string
    {
        return match($this) {
            self::PHYSICAL_PERSON => 'Pessoa Física',
            self::INDIVIDUAL_ENTREPRENEUR => 'Empresário Individual',
            self::MEI => 'Microempreendedor Individual',
            self::EIRELI => 'Empresa Individual de Responsabilidade Limitada',
            self::LTDA => 'Sociedade Limitada',
            self::SA => 'Sociedade Anônima',
            self::SC => 'Sociedade Civil',
            self::SC_LTDA => 'Sociedade Civil Limitada',
            self::SC_MEI => 'Sociedade Civil MEI',
            self::SC_EIRELI => 'Sociedade Civil EIRELI',
            self::SC_LTDA_MEI => 'Sociedade Civil Limitada MEI',
            self::SC_LTDA_EIRELI => 'Sociedade Civil Limitada EIRELI',
            self::SCP => 'Sociedade Civil de Profissionais',
            self::COOPERATIVA => 'Cooperativa',
            self::ASSOCIACAO => 'Associação',
            self::FUNDACAO => 'Fundação',
            self::ONG => 'Organização Não Governamental',
            self::OSCIP => 'Organização da Sociedade Civil de Interesse Público',
            self::EMPRESA_PUBLICA => 'Empresa Pública',
            self::SOCIEDADE_ECONOMIA_MISTA => 'Sociedade de Economia Mista',
        };
    }

    /**
     * Get all enum values as array
     */
    public static function toArray(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }
}
