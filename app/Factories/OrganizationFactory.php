<?php

namespace App\Factories;

use App\Domains\Organization;
use App\Http\Requests\Organization\UpdateRequest;
use App\Models\Organization as OrganizationModel;
use App\Enums\CompanyType;

class OrganizationFactory
{
    public function buildFromStoreArray(?array $request) : ?Organization {
        if($request === null){
            return null;
        }

        return new Organization(
            null,
            $request['name'] ?? "",
            $request['description'] ?? "",
            true,
            false,
            null, // default_flow_id
            $request['email'] ?? null,
            $request['cpfCnpj'] ?? null,
            isset($request['companyType']) ? CompanyType::from($request['companyType']) : null,
            $request['phone'] ?? null,
            $request['mobilePhone'] ?? null,
            $request['address'] ?? null,
            $request['addressNumber'] ?? null,
            $request['complement'] ?? null,
            $request['province'] ?? null,
            $request['postalCode'] ?? null,
            $request['is_courtesy'] ?? false,
            isset($request['courtesy_expires_at']) ? \Carbon\Carbon::parse($request['courtesy_expires_at']) : null,
            $request['courtesy_reason'] ?? null,
            null, // created_at
            null, // updated_at
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Organization {
        return new Organization(
            null, // id will be set in use case
            $request->name ?? "",
            $request->description ?? "",
            null, // is_active - not updated via this endpoint
            null, // is_suspended - not updated via this endpoint
            null, // default_flow_id - not updated via this endpoint
            $request->email ?? null,
            $request->cpf_cnpj ?? null,
            isset($request->company_type) ? CompanyType::from($request->company_type) : null,
            $request->phone ?? null,
            $request->mobile_phone ?? null,
            $request->address ?? null,
            $request->address_number ?? null,
            $request->complement ?? null,
            $request->province ?? null,
            $request->postal_code ?? null,
            null, // is_courtesy - not updated via this endpoint
            null, // courtesy_expires_at - not updated via this endpoint
            null, // courtesy_reason - not updated via this endpoint
            null, // created_at
            null, // updated_at
            null, // asaas
            null  // asaasCustomer
        );
    }

    public function buildFromModel(?OrganizationModel $organization) : ?Organization {
        if(!$organization){
            return null;
        }
        return new Organization(
            $organization->id ?? null,
            $organization->name ?? "",
            $organization->description ?? "",
            $organization->is_active ?? null,
            $organization->is_suspended ?? false,
            $organization->default_flow_id ?? null,
            $organization->email ?? null,
            $organization->cpf_cnpj ?? null,
            $organization->company_type ?? null,
            $organization->phone ?? null,
            $organization->mobile_phone ?? null,
            $organization->address ?? null,
            $organization->address_number ?? null,
            $organization->complement ?? null,
            $organization->province ?? null,
            $organization->postal_code ?? null,
            $organization->is_courtesy ?? false,
            $organization->courtesy_expires_at ?? null,
            $organization->courtesy_reason ?? null,
            $organization->created_at ?? null,
            $organization->updated_at ?? null,
        );
    }
}
