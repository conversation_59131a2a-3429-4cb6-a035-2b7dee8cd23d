<?php

namespace App\Http\Requests\Organization;

use App\Helpers\Traits\Response;
use App\Enums\CompanyType;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'email' => 'nullable|email|max:255',
            'cpf_cnpj' => 'nullable|string|max:20',
            'company_type' => ['nullable', Rule::enum(CompanyType::class)],
            'phone' => 'nullable|string|max:20',
            'mobile_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'address_number' => 'nullable|string|max:20',
            'complement' => 'nullable|string|max:100',
            'province' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => __('The organization name field is required.'),
            'name.string' => __('The organization name must be a string.'),
            'name.max' => __('The organization name may not be greater than 255 characters.'),
            'description.string' => __('The description must be a string.'),
            'description.max' => __('The description may not be greater than 1000 characters.'),
            'email.email' => __('The email must be a valid email address.'),
            'email.max' => __('The email may not be greater than 255 characters.'),
            'cpf_cnpj.string' => __('The CPF/CNPJ must be a string.'),
            'cpf_cnpj.max' => __('The CPF/CNPJ may not be greater than 20 characters.'),
            'company_type.enum' => __('The selected company type is invalid.'),
            'phone.string' => __('The phone must be a string.'),
            'phone.max' => __('The phone may not be greater than 20 characters.'),
            'mobile_phone.string' => __('The mobile phone must be a string.'),
            'mobile_phone.max' => __('The mobile phone may not be greater than 20 characters.'),
            'address.string' => __('The address must be a string.'),
            'address.max' => __('The address may not be greater than 255 characters.'),
            'address_number.string' => __('The address number must be a string.'),
            'address_number.max' => __('The address number may not be greater than 20 characters.'),
            'complement.string' => __('The complement must be a string.'),
            'complement.max' => __('The complement may not be greater than 100 characters.'),
            'province.string' => __('The province must be a string.'),
            'province.max' => __('The province may not be greater than 100 characters.'),
            'postal_code.string' => __('The postal code must be a string.'),
            'postal_code.max' => __('The postal code may not be greater than 20 characters.'),
        ];
    }
}
