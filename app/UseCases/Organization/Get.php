<?php

namespace App\UseCases\Organization;

use App\Domains\Organization;
use App\Repositories\OrganizationRepository;
use Exception;

class Get
{
    private OrganizationRepository $organizationRepository;

    public function __construct(OrganizationRepository $organizationRepository) {
        $this->organizationRepository = $organizationRepository;
    }

    /**
     * @param int $id
     * @return Organization
     * @throws Exception
     */
    public function perform(int $id) : Organization {
        if ($id !== request()->user()->organization_id) {
            throw new Exception(
                "This organization don't belong to this user." ,
                403
            );
        }
        return $this->organizationRepository->fetchById($id);
    }
}
