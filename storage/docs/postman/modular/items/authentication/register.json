{"name": "Register", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "if(jsonData.status === \"success\"){\r", "    postman.setEnvironmentVariable(\"TOKEN\", jsonData.data.token);\r", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"profile_id\": 1,\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"username\": \"joh<PERSON><PERSON>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"organization\": {\n        \"name\": \"My Company\",\n        \"email\": \"<EMAIL>\",\n        \"cpfCnpj\": \"12345678901\",\n        \"companyType\": \"LTDA\",\n        \"phone\": \"11999999999\",\n        \"mobilePhone\": \"11999999999\",\n        \"address\": \"Rua Example, 123\",\n        \"addressNumber\": \"123\",\n        \"complement\": \"Sala 1\",\n        \"province\": \"Centro\",\n        \"postalCode\": \"01234567\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/register", "host": ["{{URL}}"], "path": ["register"]}}, "response": []}