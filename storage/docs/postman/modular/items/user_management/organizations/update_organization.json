{"name": "Update Organization", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Company Name\",\n  \"description\": \"Updated company description\",\n  \"email\": \"<EMAIL>\",\n  \"cpf_cnpj\": \"98765432101\",\n  \"company_type\": \"SA\",\n  \"phone\": \"11888888888\",\n  \"mobile_phone\": \"11777777777\",\n  \"address\": \"Rua Updated, 456\",\n  \"address_number\": \"456\",\n  \"complement\": \"Sala 2\",\n  \"province\": \"Centro Novo\",\n  \"postal_code\": \"87654321\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/organizations/1", "host": ["{{URL}}"], "path": ["organizations", "1"]}}, "response": []}